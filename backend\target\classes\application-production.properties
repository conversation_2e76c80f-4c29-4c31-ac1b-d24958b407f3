# Production Environment Configuration
# Server Configuration
server.port=8091
server.servlet.context-path=/
server.address=0.0.0.0

# Static Resources Configuration
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.add-mappings=true
spring.mvc.static-path-pattern=/static/**

# Disable default error page to prevent conflicts
server.error.whitelabel.enabled=false

# CORS Configuration - More restrictive for production
spring.web.cors.allowed-origins=https://yourdomain.com,https://www.yourdomain.com
spring.web.cors.allowed-methods=GET,POST,PUT,PATCH,DELETE,OPTIONS,HEAD
spring.web.cors.allowed-headers=*
spring.web.cors.exposed-headers=Access-Control-Allow-Origin,Access-Control-Allow-Credentials,Access-Control-Allow-Methods,Access-Control-Allow-Headers,Access-Control-Max-Age,Authorization,X-Auth-Token
spring.web.cors.allow-credentials=true
spring.web.cors.max-age=3600

# Enable CORS for production
spring.mvc.dispatch-options-request=true

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true

# OneDrive Integration Configuration
onedrive.client.id=86756722-ad2a-4ac0-8806-e2705653949a
onedrive.tenant.id=14158288-a340-4380-88ed-a8989a932425
onedrive.client.secret=${ONEDRIVE_CLIENT_SECRET:****************************************}
onedrive.redirect.uri=https://login.microsoftonline.com/common/oauth2/nativeclient
onedrive.scope=https://graph.microsoft.com/Files.ReadWrite.All
onedrive.base.path=/personal/prathamesh_kadam_redberyltech_com/Documents/RedBeryl

# Database Configuration - Use environment variables for production
spring.datasource.url=${DATABASE_URL:*******************************************}
spring.datasource.username=${DATABASE_USERNAME:postgres}
spring.datasource.password=${DATABASE_PASSWORD:postgres}
spring.datasource.driver-class-name=org.postgresql.Driver

# Logging Configuration - Less verbose for production
logging.level.root=WARN
logging.level.org.springframework=INFO
logging.level.org.hibernate=WARN
logging.level.com.redberyl.invoiceapp=INFO

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Disable detailed logging for production
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Flyway Configuration
spring.flyway.enabled=true

# JWT Configuration - Use environment variable for production
jwt.secret=${JWT_SECRET:redberylSecretKey123456789012345678901234567890}
jwt.expiration=86400000

# Swagger Configuration - Disabled for production
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false

# Security Configuration
server.ssl.enabled=false
# server.ssl.key-store=classpath:keystore.p12
# server.ssl.key-store-password=password
# server.ssl.key-store-type=PKCS12
# server.ssl.key-alias=tomcat
